Add-Type -AssemblyName System.Drawing

$pngPath = "src-tauri/icons/icon.png"
$icoPath = "src-tauri/icons/icon.ico"

# Load the PNG image
$bitmap = New-Object System.Drawing.Bitmap($pngPath)

# Create an icon from the bitmap
$icon = [System.Drawing.Icon]::FromHandle($bitmap.GetHicon())

# Save the icon to a file
$fileStream = New-Object System.IO.FileStream($icoPath, [System.IO.FileMode]::Create)
$icon.Save($fileStream)

# Clean up
$fileStream.Close()
$bitmap.Dispose()
$icon.Dispose()

Write-Host "Icon converted successfully!"
