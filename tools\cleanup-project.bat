@echo off
echo ========================================
echo        Claudia Project Cleanup
echo ========================================
echo.

echo This script will clean up temporary files and reorganize the project.
echo.
echo What will be cleaned:
echo - Node modules cache
echo - Build artifacts (except releases)
echo - Temporary files
echo - Log files
echo.

set /p confirm="Continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo Cleanup cancelled.
    pause
    exit /b 0
)

echo.
echo Starting cleanup...
echo.

echo [1/5] Cleaning node_modules cache...
if exist "node_modules\.cache" (
    rmdir /s /q "node_modules\.cache"
    echo SUCCESS: Cleared node_modules cache
) else (
    echo INFO: No node_modules cache found
)

echo.
echo [2/5] Cleaning build artifacts...
if exist "build-artifacts\dist" (
    rmdir /s /q "build-artifacts\dist"
    echo SUCCESS: Cleared dist directory
)

if exist "src-tauri\target\debug" (
    rmdir /s /q "src-tauri\target\debug"
    echo SUCCESS: Cleared debug build
)

echo INFO: Keeping release builds

echo.
echo [3/5] Cleaning temporary files...
del /q /s "*.tmp" 2>nul
del /q /s "*.log" 2>nul
del /q /s ".DS_Store" 2>nul
del /q /s "Thumbs.db" 2>nul
echo SUCCESS: Cleaned temporary files

echo.
echo [4/5] Organizing project structure...
if not exist "docs" mkdir docs
if not exist "tools" mkdir tools
if not exist "mcp-servers" mkdir mcp-servers
if not exist "build-artifacts" mkdir build-artifacts
echo SUCCESS: Ensured directory structure

echo.
echo [5/5] Updating file permissions...
attrib +r "claudia.exe"
attrib +r "start-claudia.bat"
echo SUCCESS: Set executable permissions

echo.
echo ========================================
echo         Cleanup Complete!
echo ========================================
echo.

echo Project structure:
echo - docs/           Documentation
echo - tools/          Development tools
echo - mcp-servers/    MCP server management
echo - build-artifacts/ Build outputs
echo - src/            Frontend source
echo - src-tauri/      Backend source
echo - cc_agents/      Agent configurations
echo.

echo Main files:
echo - claudia.exe     Main application
echo - start-claudia.bat  Quick launcher
echo.

echo The project is now clean and organized!
echo.

pause
