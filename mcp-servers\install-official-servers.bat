@echo off
echo ========================================
echo     Installing Official MCP Servers
echo ========================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found. Please install Node.js first.
    pause
    exit /b 1
)

echo SUCCESS: Node.js is installed
echo.

echo Checking Claude Code installation...
claude --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Claude Code not found. Please install Claude Code first.
    pause
    exit /b 1
)

echo SUCCESS: Claude Code is installed
echo.

echo Installing official MCP servers...
echo.

echo [1/4] Installing Memory Server...
claude mcp add memory "npx -y @modelcontextprotocol/server-memory"
if %errorlevel% equ 0 (
    echo SUCCESS: Memory server installed
) else (
    echo WARNING: Memory server installation failed
)
echo.

echo [2/4] Installing Filesystem Server...
claude mcp add filesystem "npx -y @modelcontextprotocol/server-filesystem" "%USERPROFILE%\Desktop"
if %errorlevel% equ 0 (
    echo SUCCESS: Filesystem server installed
) else (
    echo WARNING: Filesystem server installation failed
)
echo.

echo [3/4] Installing Fetch Server...
claude mcp add fetch "npx -y @modelcontextprotocol/server-fetch"
if %errorlevel% equ 0 (
    echo SUCCESS: Fetch server installed
) else (
    echo WARNING: Fetch server installation failed
)
echo.

echo [4/4] Installing Time Server...
claude mcp add time "npx -y @modelcontextprotocol/server-time"
if %errorlevel% equ 0 (
    echo SUCCESS: Time server installed
) else (
    echo WARNING: Time server installation failed
)
echo.

echo ========================================
echo         Installation Complete
echo ========================================
echo.

echo Listing installed MCP servers:
claude mcp list
echo.

echo All official MCP servers have been installed!
echo You can now use them in your Claude Code sessions.
echo.
echo Next steps:
echo 1. Start a Claude Code session: claude
echo 2. Test MCP servers in your conversations
echo 3. Add custom servers in mcp-servers/custom/
echo.

echo Press any key to exit...
pause >nul
