#!/usr/bin/env node

/**
 * Basic MCP Server Template
 * 
 * This is a template for creating custom MCP servers.
 * Modify this file to add your own tools and functionality.
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

/**
 * Server Configuration
 */
const SERVER_INFO = {
  name: 'basic-mcp-server',
  version: '1.0.0',
  description: 'A basic MCP server template'
};

/**
 * Available Tools
 */
const TOOLS = [
  {
    name: 'echo',
    description: 'Echo back the input text',
    inputSchema: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          description: 'Text to echo back'
        }
      },
      required: ['text']
    }
  },
  {
    name: 'get_time',
    description: 'Get the current time',
    inputSchema: {
      type: 'object',
      properties: {
        format: {
          type: 'string',
          description: 'Time format (iso, locale, or timestamp)',
          enum: ['iso', 'locale', 'timestamp'],
          default: 'iso'
        }
      }
    }
  },
  {
    name: 'calculate',
    description: 'Perform basic mathematical calculations',
    inputSchema: {
      type: 'object',
      properties: {
        expression: {
          type: 'string',
          description: 'Mathematical expression to evaluate (e.g., "2 + 3 * 4")'
        }
      },
      required: ['expression']
    }
  }
];

/**
 * Create and configure the MCP server
 */
const server = new Server(
  SERVER_INFO,
  {
    capabilities: {
      tools: {},
    },
  }
);

/**
 * Tool Handlers
 */

// Echo tool
async function handleEcho(args) {
  const { text } = args;
  return {
    content: [
      {
        type: 'text',
        text: `Echo: ${text}`
      }
    ]
  };
}

// Get time tool
async function handleGetTime(args) {
  const { format = 'iso' } = args;
  const now = new Date();
  
  let timeString;
  switch (format) {
    case 'iso':
      timeString = now.toISOString();
      break;
    case 'locale':
      timeString = now.toLocaleString();
      break;
    case 'timestamp':
      timeString = now.getTime().toString();
      break;
    default:
      timeString = now.toISOString();
  }
  
  return {
    content: [
      {
        type: 'text',
        text: `Current time (${format}): ${timeString}`
      }
    ]
  };
}

// Calculate tool
async function handleCalculate(args) {
  const { expression } = args;
  
  try {
    // Simple expression evaluation (be careful with eval in production!)
    // This is a basic example - consider using a proper math parser
    const result = Function(`"use strict"; return (${expression})`)();
    
    return {
      content: [
        {
          type: 'text',
          text: `${expression} = ${result}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error evaluating expression "${expression}": ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * Request Handlers
 */

// List available tools
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return { tools: TOOLS };
});

// Handle tool calls
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;
  
  try {
    switch (name) {
      case 'echo':
        return await handleEcho(args);
      
      case 'get_time':
        return await handleGetTime(args);
      
      case 'calculate':
        return await handleCalculate(args);
      
      default:
        throw new Error(`Unknown tool: ${name}`);
    }
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error executing tool "${name}": ${error.message}`
        }
      ],
      isError: true
    };
  }
});

/**
 * Start the server
 */
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  
  // Log server start (to stderr so it doesn't interfere with MCP protocol)
  console.error(`${SERVER_INFO.name} v${SERVER_INFO.version} started`);
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.error('Shutting down server...');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.error('Shutting down server...');
  process.exit(0);
});

// Start the server
main().catch((error) => {
  console.error('Server error:', error);
  process.exit(1);
});
