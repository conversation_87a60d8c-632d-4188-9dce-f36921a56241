# MCP 服务器管理指南

## 🔌 什么是 MCP (Model Context Protocol)

MCP 是一个开放协议，允许 AI 模型安全地连接到外部数据源和工具。通过 MCP 服务器，Claude Code 可以访问文件系统、数据库、API 等外部资源。

## 📁 目录结构

```
mcp-servers/
├── official/          # 官方 MCP 服务器
│   ├── memory/        # 内存/知识图谱服务器
│   ├── filesystem/    # 文件系统访问服务器
│   ├── fetch/         # HTTP 请求服务器
│   ├── time/          # 时间/日期服务器
│   └── database/      # 数据库连接服务器
│
├── custom/            # 自定义 MCP 服务器
│   ├── project-tools/ # 项目特定工具
│   ├── api-clients/   # API 客户端
│   └── integrations/  # 第三方集成
│
├── configs/           # MCP 配置文件
│   ├── local.json     # 本地环境配置
│   ├── project.json   # 项目特定配置
│   └── global.json    # 全局配置
│
└── templates/         # MCP 服务器模板
    ├── basic-server/  # 基础服务器模板
    └── advanced-server/ # 高级服务器模板
```

## 🚀 快速开始

### 1. 查看已配置的服务器

```bash
claude mcp list
```

### 2. 添加新的 MCP 服务器

```bash
# 添加官方时间服务器
claude mcp add time-server "npx -y @modelcontextprotocol/server-time"

# 添加文件系统服务器
claude mcp add fs-server "npx -y @modelcontextprotocol/server-filesystem" "/path/to/directory"

# 添加内存服务器
claude mcp add memory-server "npx -y @modelcontextprotocol/server-memory"
```

### 3. 测试服务器连接

```bash
claude mcp get <server-name>
```

## 📋 官方 MCP 服务器

### 1. Memory Server (内存服务器)
- **功能**: 提供持久化内存和知识图谱
- **命令**: `npx -y @modelcontextprotocol/server-memory`
- **用途**: 存储和检索信息，构建知识图谱

### 2. Filesystem Server (文件系统服务器)
- **功能**: 安全的文件系统访问
- **命令**: `npx -y @modelcontextprotocol/server-filesystem <directory>`
- **用途**: 读取、写入、搜索文件

### 3. Fetch Server (HTTP 请求服务器)
- **功能**: 发送 HTTP 请求
- **命令**: `npx -y @modelcontextprotocol/server-fetch`
- **用途**: 访问 Web API，获取在线数据

### 4. Time Server (时间服务器)
- **功能**: 提供当前时间和日期
- **命令**: `npx -y @modelcontextprotocol/server-time`
- **用途**: 获取时间信息，时间计算

## 🛠️ 自定义 MCP 服务器

### 创建自定义服务器

1. **在 `custom/` 目录创建新文件夹**
```bash
mkdir mcp-servers/custom/my-server
cd mcp-servers/custom/my-server
```

2. **初始化项目**
```bash
npm init -y
npm install @modelcontextprotocol/sdk
```

3. **创建服务器代码**
```javascript
// index.js
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';

const server = new Server(
  {
    name: 'my-custom-server',
    version: '0.1.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// 添加工具
server.setRequestHandler('tools/list', async () => {
  return {
    tools: [
      {
        name: 'my_tool',
        description: 'My custom tool',
        inputSchema: {
          type: 'object',
          properties: {
            input: { type: 'string' }
          }
        }
      }
    ]
  };
});

// 启动服务器
const transport = new StdioServerTransport();
await server.connect(transport);
```

4. **添加到 Claude Code**
```bash
claude mcp add my-server "node /path/to/mcp-servers/custom/my-server/index.js"
```

## ⚙️ 配置管理

### 本地配置 (`configs/local.json`)
```json
{
  "servers": {
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "env": {}
    },
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:\\Projects"],
      "env": {}
    }
  }
}
```

### 项目配置 (`.mcp.json`)
```json
{
  "mcpServers": {
    "project-tools": {
      "command": "node",
      "args": ["./mcp-servers/custom/project-tools/index.js"],
      "env": {
        "PROJECT_ROOT": "."
      }
    }
  }
}
```

## 🔧 管理命令

### 基本操作
```bash
# 列出所有服务器
claude mcp list

# 添加服务器
claude mcp add <name> "<command>" [args...]

# 获取服务器详情
claude mcp get <name>

# 移除服务器
claude mcp remove <name>

# 重置项目选择
claude mcp reset-project-choices
```

### 高级操作
```bash
# 添加 JSON 配置
claude mcp add-json <name> '<json-config>' -s <scope>

# 从 Claude Desktop 导入
claude mcp add-from-claude-desktop

# 启动 MCP 服务
claude mcp serve
```

## 🎯 最佳实践

### 1. 服务器命名
- 使用描述性名称：`project-db`, `api-client`, `file-manager`
- 避免特殊字符和空格
- 使用小写字母和连字符

### 2. 安全考虑
- 限制文件系统访问范围
- 验证输入参数
- 使用环境变量存储敏感信息

### 3. 性能优化
- 缓存频繁访问的数据
- 限制并发连接数
- 实现超时机制

### 4. 错误处理
- 提供清晰的错误消息
- 实现重试机制
- 记录详细日志

## 📚 资源链接

- [MCP 官方文档](https://modelcontextprotocol.io/)
- [MCP SDK 文档](https://github.com/modelcontextprotocol/typescript-sdk)
- [官方服务器示例](https://github.com/modelcontextprotocol/servers)
- [Claude Code MCP 指南](https://docs.anthropic.com/claude/docs/mcp)

## 🆘 故障排除

### 常见问题

1. **服务器连接失败**
   - 检查命令路径是否正确
   - 验证 Node.js 环境
   - 查看错误日志

2. **权限问题**
   - 确保文件访问权限
   - 检查环境变量设置
   - 验证网络访问权限

3. **性能问题**
   - 检查服务器资源使用
   - 优化数据查询
   - 实现缓存机制

### 调试技巧
```bash
# 启用详细日志
export MCP_LOG_LEVEL=debug

# 测试服务器
node your-server.js < test-input.json

# 检查服务器状态
claude mcp list --verbose
```

---

**提示**: 将你的自定义 MCP 服务器放在 `custom/` 目录中，并在 `configs/` 目录中管理配置文件。
