# Basic MCP Server Template

这是一个基础的 MCP 服务器模板，可以作为创建自定义 MCP 服务器的起点。

## 🚀 快速开始

### 1. 复制模板
```bash
cp -r mcp-servers/templates/basic-server mcp-servers/custom/my-server
cd mcp-servers/custom/my-server
```

### 2. 安装依赖
```bash
npm install
```

### 3. 测试服务器
```bash
npm start
```

### 4. 添加到 Claude Code
```bash
claude mcp add my-server "node /path/to/mcp-servers/custom/my-server/index.js"
```

## 🛠️ 包含的工具

### 1. Echo Tool
- **功能**: 回显输入的文本
- **参数**: `text` (string) - 要回显的文本
- **示例**: `echo("Hello World")` → "Echo: Hello World"

### 2. Get Time Tool
- **功能**: 获取当前时间
- **参数**: `format` (string) - 时间格式 (iso/locale/timestamp)
- **示例**: `get_time("iso")` → "2024-01-01T12:00:00.000Z"

### 3. <PERSON><PERSON> Tool
- **功能**: 执行基本数学计算
- **参数**: `expression` (string) - 数学表达式
- **示例**: `calculate("2 + 3 * 4")` → "2 + 3 * 4 = 14"

## 📝 自定义服务器

### 添加新工具

1. **在 TOOLS 数组中定义工具**:
```javascript
{
  name: 'my_tool',
  description: 'My custom tool',
  inputSchema: {
    type: 'object',
    properties: {
      param1: { type: 'string', description: 'Parameter 1' }
    },
    required: ['param1']
  }
}
```

2. **创建工具处理函数**:
```javascript
async function handleMyTool(args) {
  const { param1 } = args;
  
  // 你的逻辑
  const result = processParam(param1);
  
  return {
    content: [
      {
        type: 'text',
        text: `Result: ${result}`
      }
    ]
  };
}
```

3. **在请求处理器中添加案例**:
```javascript
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;
  
  switch (name) {
    case 'my_tool':
      return await handleMyTool(args);
    // ... 其他工具
  }
});
```

### 添加资源支持

如果需要提供资源（如文件、数据），可以添加资源处理器：

```javascript
import {
  ListResourcesRequestSchema,
  ReadResourceRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

// 列出资源
server.setRequestHandler(ListResourcesRequestSchema, async () => {
  return {
    resources: [
      {
        uri: 'file://example.txt',
        name: 'Example File',
        description: 'An example text file',
        mimeType: 'text/plain'
      }
    ]
  };
});

// 读取资源
server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
  const { uri } = request.params;
  
  if (uri === 'file://example.txt') {
    return {
      contents: [
        {
          uri,
          mimeType: 'text/plain',
          text: 'This is example content'
        }
      ]
    };
  }
  
  throw new Error(`Resource not found: ${uri}`);
});
```

## 🔧 开发技巧

### 调试
```bash
# 启用详细日志
export MCP_LOG_LEVEL=debug
npm start

# 使用 watch 模式
npm run dev
```

### 测试
```bash
# 运行测试
npm test

# 手动测试工具
echo '{"method":"tools/list","params":{}}' | npm start
```

### 错误处理
- 总是包装工具逻辑在 try-catch 中
- 返回有意义的错误消息
- 使用 `isError: true` 标记错误响应

### 性能优化
- 缓存频繁访问的数据
- 使用异步操作
- 限制资源使用

## 📚 参考资源

- [MCP 官方文档](https://modelcontextprotocol.io/)
- [MCP SDK 文档](https://github.com/modelcontextprotocol/typescript-sdk)
- [更多示例](https://github.com/modelcontextprotocol/servers)

## 🎯 下一步

1. 根据你的需求修改工具
2. 添加错误处理和验证
3. 编写测试
4. 添加到 Claude Code
5. 在实际项目中测试
