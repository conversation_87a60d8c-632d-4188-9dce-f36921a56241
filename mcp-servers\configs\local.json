{"description": "本地环境 MCP 服务器配置", "version": "1.0.0", "servers": {"memory": {"description": "内存和知识图谱服务器", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {}, "scope": "local", "enabled": true}, "filesystem": {"description": "文件系统访问服务器", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:\\Users\\<USER>\\Desktop"], "env": {}, "scope": "local", "enabled": true}, "fetch": {"description": "HTTP 请求服务器", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"], "env": {}, "scope": "local", "enabled": true}, "time": {"description": "时间和日期服务器", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-time"], "env": {}, "scope": "local", "enabled": true}}, "settings": {"auto_start": false, "timeout": 30000, "retry_count": 3, "log_level": "info"}}