{"description": "Claudia 项目特定 MCP 服务器配置", "version": "1.0.0", "project": {"name": "claudia", "root": "C:\\Users\\<USER>\\Desktop\\claudia", "type": "tauri-app"}, "mcpServers": {"project-filesystem": {"description": "项目文件系统访问", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:\\Users\\<USER>\\Desktop\\claudia"], "env": {"PROJECT_ROOT": "C:\\Users\\<USER>\\Desktop\\claudia"}}, "git-tools": {"description": "Git 操作工具", "command": "node", "args": ["./mcp-servers/custom/git-tools/index.js"], "env": {"GIT_REPO": "C:\\Users\\<USER>\\Desktop\\claudia"}}, "build-tools": {"description": "构建和部署工具", "command": "node", "args": ["./mcp-servers/custom/build-tools/index.js"], "env": {"BUILD_DIR": "./build-artifacts", "NODE_ENV": "production"}}, "tauri-tools": {"description": "Tauri 开发工具", "command": "node", "args": ["./mcp-servers/custom/tauri-tools/index.js"], "env": {"TAURI_CONFIG": "./src-tauri/tauri.conf.json"}}}, "settings": {"auto_approve": ["project-filesystem"], "restricted_paths": ["node_modules", ".git", "target"], "allowed_operations": ["read", "write", "execute"]}}