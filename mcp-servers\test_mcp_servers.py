#!/usr/bin/env python3
"""
测试 MCP 服务器连接和功能
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from mcp_use import MCPClient
    print("✅ mcp-use 库导入成功")
except ImportError as e:
    print(f"❌ 无法导入 mcp-use: {e}")
    print("请确保已安装 mcp-use: pip install mcp-use")
    sys.exit(1)

async def test_time_server():
    """测试时间服务器"""
    print("\n🕐 测试时间服务器...")
    
    config = {
        "mcpServers": {
            "time": {
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-time"]
            }
        }
    }
    
    try:
        client = MCPClient.from_dict(config)

        # 尝试连接并获取工具
        sessions = await client.get_or_create_sessions()
        if sessions:
            session = list(sessions.values())[0]
            tools = await session.list_tools()
            print(f"✅ 时间服务器连接成功，可用工具: {len(tools.tools) if hasattr(tools, 'tools') else 0}")
            if hasattr(tools, 'tools'):
                for tool in tools.tools:
                    print(f"   - {tool.name}: {tool.description}")

        await client.close_all_sessions()
        return True
        
    except Exception as e:
        print(f"❌ 时间服务器连接失败: {e}")
        return False

async def test_filesystem_server():
    """测试文件系统服务器"""
    print("\n📁 测试文件系统服务器...")
    
    config = {
        "mcpServers": {
            "filesystem": {
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-filesystem", str(Path.cwd())]
            }
        }
    }
    
    try:
        client = MCPClient.from_dict(config)

        # 尝试连接并获取工具
        sessions = await client.get_or_create_sessions()
        if sessions:
            session = list(sessions.values())[0]
            tools = await session.list_tools()
            print(f"✅ 文件系统服务器连接成功，可用工具: {len(tools.tools) if hasattr(tools, 'tools') else 0}")
            if hasattr(tools, 'tools'):
                for tool in tools.tools:
                    print(f"   - {tool.name}: {tool.description}")

        await client.close_all_sessions()
        return True
        
    except Exception as e:
        print(f"❌ 文件系统服务器连接失败: {e}")
        return False

async def test_memory_server():
    """测试内存服务器"""
    print("\n🧠 测试内存服务器...")
    
    config = {
        "mcpServers": {
            "memory": {
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-memory"]
            }
        }
    }
    
    try:
        client = MCPClient.from_dict(config)

        # 尝试连接并获取工具
        sessions = await client.get_or_create_sessions()
        if sessions:
            session = list(sessions.values())[0]
            tools = await session.list_tools()
            print(f"✅ 内存服务器连接成功，可用工具: {len(tools.tools) if hasattr(tools, 'tools') else 0}")
            if hasattr(tools, 'tools'):
                for tool in tools.tools:
                    print(f"   - {tool.name}: {tool.description}")

        await client.close_all_sessions()
        return True
        
    except Exception as e:
        print(f"❌ 内存服务器连接失败: {e}")
        return False

async def test_fetch_server():
    """测试网络请求服务器"""
    print("\n🌐 测试网络请求服务器...")
    
    config = {
        "mcpServers": {
            "fetch": {
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-fetch"]
            }
        }
    }
    
    try:
        client = MCPClient.from_dict(config)

        # 尝试连接并获取工具
        sessions = await client.get_or_create_sessions()
        if sessions:
            session = list(sessions.values())[0]
            tools = await session.list_tools()
            print(f"✅ 网络请求服务器连接成功，可用工具: {len(tools.tools) if hasattr(tools, 'tools') else 0}")
            if hasattr(tools, 'tools'):
                for tool in tools.tools:
                    print(f"   - {tool.name}: {tool.description}")

        await client.close_all_sessions()
        return True
        
    except Exception as e:
        print(f"❌ 网络请求服务器连接失败: {e}")
        return False

async def test_browser_tools_server():
    """测试浏览器工具服务器"""
    print("\n🌐 测试浏览器工具服务器...")
    
    config = {
        "mcpServers": {
            "browser-tools": {
                "command": "npx",
                "args": ["@agentdeskai/browser-tools-mcp"]
            }
        }
    }
    
    try:
        client = MCPClient.from_dict(config)

        # 尝试连接并获取工具
        sessions = await client.get_or_create_sessions()
        if sessions:
            session = list(sessions.values())[0]
            tools = await session.list_tools()
            print(f"✅ 浏览器工具服务器连接成功，可用工具: {len(tools.tools) if hasattr(tools, 'tools') else 0}")
            if hasattr(tools, 'tools'):
                for tool in tools.tools:
                    print(f"   - {tool.name}: {tool.description}")

        await client.close_all_sessions()
        return True
        
    except Exception as e:
        print(f"❌ 浏览器工具服务器连接失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试 MCP 服务器...")
    print(f"📍 当前目录: {Path.cwd()}")
    
    # 检查 Node.js 环境
    import subprocess
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js 版本: {result.stdout.strip()}")
        else:
            print("❌ Node.js 未找到")
            return
    except FileNotFoundError:
        print("❌ Node.js 未安装")
        return
    
    # 测试各个服务器
    results = []
    
    # 测试时间服务器
    results.append(await test_time_server())
    
    # 测试文件系统服务器
    results.append(await test_filesystem_server())
    
    # 测试内存服务器
    results.append(await test_memory_server())
    
    # 测试网络请求服务器
    results.append(await test_fetch_server())
    
    # 测试浏览器工具服务器
    results.append(await test_browser_tools_server())
    
    # 总结结果
    successful = sum(results)
    total = len(results)
    
    print(f"\n📊 测试结果总结:")
    print(f"✅ 成功: {successful}/{total}")
    print(f"❌ 失败: {total - successful}/{total}")
    
    if successful == total:
        print("🎉 所有 MCP 服务器测试通过！")
    else:
        print("⚠️ 部分 MCP 服务器测试失败，请检查配置和依赖")

if __name__ == "__main__":
    asyncio.run(main())
