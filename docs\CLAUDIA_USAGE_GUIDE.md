# Claudia 使用指南

## 🎯 项目概述

Claudia 是一个强大的 GUI 应用程序，为 Claude Code 提供可视化界面和高级功能。它包含项目管理、会话管理、MCP 服务器管理、使用分析等功能。

## 📁 项目位置

- **主目录**: `C:\Users\<USER>\Desktop\claudia`
- **可执行文件**: `C:\Users\<USER>\Desktop\claudia\claudia.exe`
- **源代码可执行文件**: `C:\Users\<USER>\Desktop\claudia\src-tauri\target\release\claudia.exe`

## 🚀 启动应用程序

### 方法 1: 双击启动
```
导航到 C:\Users\<USER>\Desktop\claudia
双击 claudia.exe
```

### 方法 2: 命令行启动
```cmd
cd C:\Users\<USER>\Desktop\claudia
claudia.exe
```

### 方法 3: PowerShell启动
```powershell
cd C:\Users\<USER>\Desktop\claudia
.\claudia.exe
```

## 🔧 核心功能

### 1. 项目管理 (CC Projects)
- 浏览和管理 Claude Code 项目
- 查看项目文件结构
- 快速访问项目目录

### 2. 代理管理 (Agents)
- 创建自定义 AI 代理
- 管理代理提示和配置
- 执行代理任务

### 3. 使用分析 (Usage Dashboard)
- 监控 Claude API 使用情况
- 查看成本统计
- 分析使用模式

### 4. CLAUDE.md 管理
- 内置 Markdown 编辑器
- 管理项目说明文件
- 语法高亮和预览

### 5. MCP 服务器管理
- 添加和配置 MCP 服务器
- 管理服务器连接
- 测试服务器功能

### 6. 设置管理 (Settings)
- 配置应用程序偏好
- 管理 Claude 安装路径
- 自定义界面主题

## 🔌 MCP (Model Context Protocol) 功能

### 已配置的 MCP 服务器

1. **Memory Server**: `npx -y @modelcontextprotocol/server-memory`
2. **Filesystem Server**: `npx -y @modelcontextprotocol/server-filesystem`
3. **Fetch Server**: `npx -y @modelcontextprotocol/server-fetch`
4. **Time Server**: `npx -y @modelcontextprotocol/server-time`

### 添加新的 MCP 服务器

```bash
# 使用 Claude Code CLI
claude mcp add <server-name> "<command>" [args...]

# 示例：添加时间服务器
claude mcp add my-time-server "npx -y @modelcontextprotocol/server-time"
```

### 管理 MCP 服务器

```bash
# 列出所有服务器
claude mcp list

# 获取服务器详情
claude mcp get <server-name>

# 移除服务器
claude mcp remove <server-name>

# 测试服务器连接
claude mcp test-connection <server-name>
```

## 🛠️ 故障排除

### 问题 1: "Failed to load Claude installations"

**症状**: 设置页面显示无法加载 Claude 安装

**解决方案**:
1. 确保 Claude Code 已正确安装
2. 检查 PATH 环境变量包含 Claude 路径
3. 重启 Claudia 应用程序

**验证 Claude 安装**:
```cmd
claude --version
```

### 问题 2: MCP 服务器连接失败

**症状**: MCP 服务器显示 "Failed to connect"

**说明**: 这是正常现象，MCP 服务器只在 Claude Code 会话中按需启动

**验证 MCP 配置**:
```cmd
claude mcp list
claude mcp get <server-name>
```

### 问题 3: 应用程序无法启动

**可能原因**:
1. 缺少 WebView2 运行时
2. 权限问题
3. 端口冲突

**解决步骤**:
1. 安装 Microsoft Edge WebView2 运行时
2. 以管理员身份运行
3. 检查防火墙设置

### 问题 4: Claude Code 权限提示

**症状**: Claude Code 要求文件夹权限

**解决方案**:
1. 选择 "1. Yes, proceed"
2. 按 Enter 确认
3. 这是正常的安全检查

## 📊 技术规格

- **前端**: React 18 + TypeScript + Vite 6
- **后端**: Rust + Tauri 2
- **UI 框架**: Tailwind CSS v4 + shadcn/ui
- **数据库**: SQLite
- **包管理器**: Bun

## 🔄 开发模式

### 启动开发服务器
```bash
cd C:\Users\<USER>\Desktop\claudia
bun run tauri dev
```

### 构建生产版本
```bash
bun run tauri build
```

## 📝 日志和调试

### 查看应用程序日志
- Windows: `%APPDATA%\claudia\logs\`
- 开发模式: 控制台输出

### 启用详细日志
```bash
# 设置环境变量
set RUST_LOG=debug
claudia.exe
```

## 🆘 获取帮助

如果遇到问题：

1. 检查本指南的故障排除部分
2. 验证 Claude Code 安装：`claude --version`
3. 检查 MCP 配置：`claude mcp list`
4. 重启应用程序
5. 查看应用程序日志

## 🎉 成功验证

应用程序成功部署的标志：
- ✅ Claudia GUI 界面正常显示
- ✅ 可以访问所有功能模块
- ✅ Claude Code CLI 正常工作
- ✅ MCP 服务器配置正确
- ✅ 可以创建和管理项目

---

**版本**: 0.1.0  
**构建日期**: 2025-08-04  
**支持平台**: Windows 10+
