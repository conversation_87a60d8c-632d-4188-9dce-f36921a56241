# BrowserTools MCP Server

## 📖 概述

BrowserTools MCP 服务器提供 AI 驱动的浏览器工具集成，允许 Claude 与浏览器进行交互，执行调试、分析和自动化任务。

## 🚀 安装状态

- ✅ **已安装**: `@agentdeskai/browser-tools-mcp@1.2.1`
- ✅ **已配置**: 添加到 Claude Code MCP 配置
- ✅ **命令**: `npx @agentdeskai/browser-tools-mcp`

## 🔧 功能特性

### 浏览器调试
- **控制台日志访问** - 获取浏览器控制台输出
- **网络请求分析** - 监控和分析网络请求
- **错误检测** - 捕获和分析 JavaScript 错误

### 页面分析
- **截图捕获** - 获取页面截图
- **元素选择和检查** - DOM 元素分析
- **实时状态监控** - 浏览器状态实时监控

### 审计功能
- **可访问性审计** - WCAG 合规性检查
- **性能审计** - 页面性能分析
- **SEO 审计** - 搜索引擎优化检查
- **最佳实践审计** - 代码质量和最佳实践检查

## 🛠️ 可用工具

### 日志和错误
- `mcp_getConsoleLogs` - 获取浏览器控制台日志
- `mcp_getConsoleErrors` - 获取控制台错误
- `mcp_getNetworkErrors` - 获取网络错误日志
- `mcp_getNetworkSuccess` - 获取成功的网络请求
- `mcp_getNetworkLogs` - 获取所有网络日志

### 元素和状态
- `mcp_getSelectedElement` - 获取当前选中的 DOM 元素

### 审计工具
- `mcp_runAccessibilityAudit` - 运行可访问性审计
- `mcp_runPerformanceAudit` - 运行性能审计
- `mcp_runSEOAudit` - 运行 SEO 审计
- `mcp_runBestPracticesAudit` - 运行最佳实践审计

## 📋 使用前提

### 必需组件
1. **Node.js 14+** - ✅ 已安装
2. **Browser Tools Server** - 需要运行浏览器工具服务器
3. **Chrome/Chromium** - 审计功能需要

### 启动步骤
1. **启动 Browser Tools Server**:
   ```bash
   npx @agentdeskai/browser-tools-server
   ```

2. **启动 MCP 服务器**:
   ```bash
   npx @agentdeskai/browser-tools-mcp
   ```

## 🎯 使用场景

### Web 开发调试
- 分析页面性能问题
- 检查 JavaScript 错误
- 监控网络请求

### 质量保证
- 自动化可访问性测试
- SEO 优化检查
- 最佳实践验证

### 用户体验分析
- 页面加载性能分析
- 用户交互监控
- 错误追踪和分析

## ⚠️ 注意事项

1. **依赖服务**: 需要先启动 Browser Tools Server
2. **浏览器要求**: Chrome/Chromium 用于审计功能
3. **权限**: 可能需要浏览器扩展权限

## 🔗 相关链接

- [NPM 包](https://www.npmjs.com/package/@agentdeskai/browser-tools-mcp)
- [GitHub 仓库](https://github.com/agentdeskai/browser-tools-mcp)
- [MCP 协议文档](https://modelcontextprotocol.io/)

## 📝 配置信息

```json
{
  "name": "browser-tools",
  "command": "npx @agentdeskai/browser-tools-mcp",
  "type": "stdio",
  "scope": "local"
}
```
