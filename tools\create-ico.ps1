# Create a proper ICO file from PNG
Add-Type -AssemblyName System.Drawing

$pngPath = "src-tauri/icons/icon.png"
$icoPath = "src-tauri/icons/icon.ico"

# Load the PNG image
$image = [System.Drawing.Image]::FromFile((Resolve-Path $pngPath).Path)

# Create a new bitmap with the desired size (32x32 for ICO)
$size = New-Object System.Drawing.Size(32, 32)
$bitmap = New-Object System.Drawing.Bitmap($size.Width, $size.Height)

# Create graphics object and draw the resized image
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)
$graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
$graphics.DrawImage($image, 0, 0, $size.Width, $size.Height)

# Convert to icon
$hIcon = $bitmap.GetHicon()
$icon = [System.Drawing.Icon]::FromHandle($hIcon)

# Save as ICO file
$fileStream = [System.IO.FileStream]::new($icoPath, [System.IO.FileMode]::Create)
$icon.Save($fileStream)

# Clean up
$fileStream.Close()
$graphics.Dispose()
$bitmap.Dispose()
$image.Dispose()
$icon.Dispose()

Write-Host "ICO file created successfully at $icoPath"
