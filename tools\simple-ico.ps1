# Simple ICO creation that should be compatible with Tauri
Add-Type -AssemblyName System.Drawing

$pngPath = "src-tauri/icons/32x32.png"  # Use the 32x32 PNG which is more suitable for ICO
$icoPath = "src-tauri/icons/icon.ico"

try {
    # Load the 32x32 PNG image
    $image = [System.Drawing.Image]::FromFile((Resolve-Path $pngPath).Path)
    
    # Create a bitmap from the image
    $bitmap = New-Object System.Drawing.Bitmap($image)
    
    # Get the icon handle
    $hIcon = $bitmap.GetHicon()
    
    # Create icon from handle
    $icon = [System.Drawing.Icon]::FromHandle($hIcon)
    
    # Save to file
    $fileStream = [System.IO.FileStream]::new($icoPath, [System.IO.FileMode]::Create)
    $icon.Save($fileStream)
    
    # Clean up
    $fileStream.Close()
    $fileStream.Dispose()
    $icon.Dispose()
    $bitmap.Dispose()
    $image.Dispose()
    
    Write-Host "ICO file created successfully from 32x32 PNG"
}
catch {
    Write-Host "Error creating ICO file: $($_.Exception.Message)"
}
