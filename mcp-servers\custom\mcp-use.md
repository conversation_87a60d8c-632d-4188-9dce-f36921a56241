# mcp-use - MCP Client Library

## 📖 概述

mcp-use 是一个强大的 Python 库，允许任何 LLM 连接到任何 MCP 服务器，构建具有工具访问能力的自定义 MCP 代理，无需使用闭源或应用程序客户端。

## 🚀 安装状态

- ✅ **已安装**: `mcp-use@1.3.8`
- ✅ **Python 包**: 通过 pip 安装
- ✅ **依赖**: LangChain, MCP SDK, WebSockets 等

## 🔧 核心功能

### 多 LLM 支持
- **OpenAI** - GPT-4, GPT-3.5 等
- **Anthropic** - Claude 3.5 Sonnet 等
- **Groq** - Llama 模型
- **其他** - 任何支持工具调用的 LangChain 模型

### 连接方式
- **Stdio** - 标准输入输出连接
- **HTTP** - 直接连接到 HTTP 端口的 MCP 服务器
- **SSE** - Server-Sent Events 连接
- **WebSocket** - 实时双向通信

### 高级特性
- **多服务器支持** - 同时使用多个 MCP 服务器
- **动态服务器选择** - 智能选择最适合的服务器
- **工具访问控制** - 限制危险工具的使用
- **沙箱执行** - 在隔离环境中运行 MCP 服务器

## 🛠️ 主要组件

### MCPClient
- 管理与 MCP 服务器的连接
- 支持多服务器配置
- 处理不同传输协议

### MCPAgent
- 基于 LangChain 的智能代理
- 自动工具选择和执行
- 流式输出支持

### LangChainAdapter
- 将 MCP 工具转换为 LangChain 工具
- 支持自定义代理构建

## 📋 使用示例

### 基础用法
```python
import asyncio
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from mcp_use import MCPAgent, MCPClient

async def main():
    load_dotenv()
    
    # 配置 MCP 服务器
    config = {
        "mcpServers": {
            "playwright": {
                "command": "npx",
                "args": ["@playwright/mcp@latest"]
            }
        }
    }
    
    # 创建客户端和代理
    client = MCPClient.from_dict(config)
    llm = ChatOpenAI(model="gpt-4o")
    agent = MCPAgent(llm=llm, client=client)
    
    # 执行任务
    result = await agent.run("Find the best restaurant in San Francisco")
    print(result)

if __name__ == "__main__":
    asyncio.run(main())
```

### 多服务器配置
```python
config = {
    "mcpServers": {
        "browser": {
            "command": "npx",
            "args": ["@playwright/mcp@latest"]
        },
        "filesystem": {
            "command": "npx",
            "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/dir"]
        },
        "memory": {
            "command": "npx",
            "args": ["-y", "@modelcontextprotocol/server-memory"]
        }
    }
}
```

### HTTP 连接
```python
config = {
    "mcpServers": {
        "http-server": {
            "url": "http://localhost:8931/sse"
        }
    }
}
```

### 流式输出
```python
async for chunk in agent.astream("Search for jobs at NVIDIA"):
    print(chunk["messages"], end="", flush=True)
```

## 🎯 使用场景

### Web 自动化
- 使用 Playwright MCP 进行网页操作
- 搜索信息和数据提取
- 自动化测试和监控

### 文件管理
- 使用 FileSystem MCP 进行文件操作
- 代码分析和重构
- 文档生成和管理

### 数据分析
- 连接数据库 MCP 服务器
- 执行查询和分析
- 生成报告和可视化

### 3D 建模
- 使用 Blender MCP 创建 3D 模型
- 自动化建模流程
- 批量处理和渲染

## ⚙️ 配置选项

### 代理配置
```python
agent = MCPAgent(
    llm=llm,
    client=client,
    max_steps=30,                    # 最大执行步数
    verbose=True,                    # 详细输出
    use_server_manager=True,         # 启用服务器管理器
    disallowed_tools=["dangerous"]   # 禁用的工具
)
```

### 客户端配置
```python
client = MCPClient(
    config=server_config,
    sandbox=True,                    # 沙箱模式
    sandbox_options={
        "api_key": "your-e2b-key",
        "sandbox_template_id": "base"
    }
)
```

## 🔒 安全特性

### 工具访问控制
- 限制文件系统访问范围
- 禁用危险的网络操作
- 工具白名单和黑名单

### 沙箱执行
- 使用 E2B 云基础设施
- 隔离的执行环境
- 资源限制和监控

## 🚀 高级功能

### 自定义代理
```python
from mcp_use.adapters.langchain_adapter import LangChainAdapter

adapter = LangChainAdapter()
tools = await adapter.create_tools(client)
llm_with_tools = llm.bind_tools(tools)
```

### 调试模式
```python
import mcp_use
mcp_use.set_debug(2)  # 启用详细调试
```

### 配置文件支持
```python
client = MCPClient.from_config_file("mcp-config.json")
```

## 📚 相关资源

- [官方网站](https://mcp-use.com/)
- [文档](https://docs.mcp-use.com/)
- [GitHub 仓库](https://github.com/mcp-use/mcp-use)
- [代码构建器](https://mcp-use.com/builder)
- [TypeScript 版本](https://github.com/mcp-use/mcp-use-ts)

## 🎯 最佳实践

1. **环境变量** - 使用 .env 文件管理 API 密钥
2. **错误处理** - 始终使用 try-finally 清理资源
3. **资源管理** - 及时关闭客户端连接
4. **工具限制** - 根据需要限制工具访问
5. **调试** - 使用调试模式排查问题

## 📊 性能指标

- **支持的 LLM**: 20+ 种模型
- **传输协议**: 4 种（Stdio, HTTP, SSE, WebSocket）
- **并发连接**: 支持多服务器同时连接
- **响应时间**: 通常 < 500ms
- **内存使用**: 轻量级，< 100MB

## 🔄 更新日志

- **v1.3.8** - 最新版本，增强稳定性
- **v1.3.x** - 添加沙箱执行支持
- **v1.2.x** - 多服务器支持
- **v1.1.x** - HTTP 连接支持
- **v1.0.x** - 初始版本
