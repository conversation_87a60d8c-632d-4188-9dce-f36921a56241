#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <windows.h>

int main(int argc, char *argv[]) {
    char command[4096] = "powershell.exe -ExecutionPolicy Bypass -File \"C:\\nvm4w\\nodejs\\claude.ps1\"";
    
    // Add all arguments to the command
    for (int i = 1; i < argc; i++) {
        strcat(command, " \"");
        strcat(command, argv[i]);
        strcat(command, "\"");
    }
    
    // Execute the command
    return system(command);
}
