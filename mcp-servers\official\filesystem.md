# FileSystem MCP Server

## 📖 概述

FileSystem MCP 服务器是官方的文件系统访问服务器，提供安全的文件和目录操作功能，允许 Claude 读取、写入和管理文件系统。

## 🚀 安装状态

- ✅ **已安装**: `@modelcontextprotocol/server-filesystem`
- ✅ **已配置**: 添加到 Claude Code MCP 配置
- ✅ **命令**: `npx -y @modelcontextprotocol/server-filesystem`
- ✅ **访问路径**: `C:\Users\<USER>\Desktop`

## 🔧 功能特性

### 文件操作
- **文件读取** - 读取文本文件内容
- **文件写入** - 创建和修改文件
- **文件删除** - 安全删除文件
- **文件移动** - 移动和重命名文件

### 目录操作
- **目录浏览** - 列出目录内容
- **目录创建** - 创建新目录
- **目录删除** - 删除空目录
- **递归操作** - 支持递归目录操作

### 文件信息
- **文件属性** - 获取文件大小、修改时间等
- **权限检查** - 检查文件读写权限
- **文件类型** - 识别文件类型和扩展名

## 🛠️ 可用工具

### 文件读写
- `read_file` - 读取文件内容
- `write_file` - 写入文件内容
- `create_file` - 创建新文件
- `delete_file` - 删除文件

### 目录管理
- `list_directory` - 列出目录内容
- `create_directory` - 创建目录
- `delete_directory` - 删除目录
- `get_directory_tree` - 获取目录树结构

### 文件信息
- `get_file_info` - 获取文件详细信息
- `file_exists` - 检查文件是否存在
- `get_file_size` - 获取文件大小
- `get_file_permissions` - 获取文件权限

### 搜索功能
- `search_files` - 按名称搜索文件
- `find_files_by_pattern` - 按模式搜索文件
- `grep_files` - 在文件中搜索文本

## 📋 安全特性

### 访问控制
- **路径限制** - 只能访问指定目录及其子目录
- **安全检查** - 防止路径遍历攻击
- **权限验证** - 检查文件操作权限

### 文件类型限制
- **文本文件** - 主要支持文本文件操作
- **二进制文件** - 有限的二进制文件支持
- **大文件处理** - 大文件的分块处理

## 🎯 使用场景

### 代码开发
- 读取和修改源代码文件
- 创建新的项目文件
- 管理配置文件

### 文档管理
- 创建和编辑 Markdown 文档
- 管理项目文档结构
- 批量文件操作

### 项目维护
- 清理临时文件
- 重组项目结构
- 备份重要文件

## ⚠️ 注意事项

1. **访问范围**: 仅限于配置的根目录及其子目录
2. **文件大小**: 大文件可能需要分块处理
3. **权限**: 需要适当的文件系统权限
4. **编码**: 默认使用 UTF-8 编码

## 🔧 配置选项

### 基本配置
```json
{
  "name": "filesystem",
  "command": "npx -y @modelcontextprotocol/server-filesystem",
  "args": ["C:\\Users\\<USER>\\Desktop"],
  "type": "stdio",
  "scope": "local"
}
```

### 高级配置
```json
{
  "name": "filesystem-project",
  "command": "npx -y @modelcontextprotocol/server-filesystem",
  "args": [
    "C:\\Users\\<USER>\\Desktop\\claudia",
    "--allow-write",
    "--max-file-size=10MB"
  ],
  "env": {
    "FS_ENCODING": "utf-8"
  }
}
```

## 📝 使用示例

### 读取文件
```javascript
// 通过 Claude 使用
"请读取 package.json 文件的内容"
```

### 创建文件
```javascript
// 通过 Claude 使用
"请创建一个新的 README.md 文件，内容是项目介绍"
```

### 目录操作
```javascript
// 通过 Claude 使用
"请列出当前目录下的所有 .js 文件"
```

## 🔗 相关链接

- [官方文档](https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem)
- [MCP 协议](https://modelcontextprotocol.io/)
- [安全指南](https://docs.anthropic.com/claude/docs/mcp-security)

## 📊 性能指标

- **文件读取**: 支持最大 10MB 文件
- **并发操作**: 支持多个文件同时操作
- **响应时间**: 通常 < 100ms
- **内存使用**: 低内存占用
