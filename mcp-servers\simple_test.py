#!/usr/bin/env python3
"""
简单的 MCP 服务器测试
"""

import asyncio
import sys
from pathlib import Path

try:
    from mcp_use import MCPClient, MCPAgent
    print("✅ mcp-use 库导入成功")
except ImportError as e:
    print(f"❌ 无法导入 mcp-use: {e}")
    sys.exit(1)

async def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 测试基本功能...")
    
    # 简单的配置测试
    config = {
        "mcpServers": {
            "test": {
                "command": "echo",
                "args": ["Hello MCP"]
            }
        }
    }
    
    try:
        # 创建客户端
        client = MCPClient.from_dict(config)
        print("✅ MCPClient 创建成功")
        
        # 检查客户端属性
        print(f"✅ 客户端配置: {len(config['mcpServers'])} 个服务器")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

async def test_time_server_simple():
    """简单测试时间服务器"""
    print("\n🕐 简单测试时间服务器...")
    
    config = {
        "mcpServers": {
            "time": {
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-time"]
            }
        }
    }
    
    try:
        client = MCPClient.from_dict(config)
        print("✅ 时间服务器客户端创建成功")
        
        # 尝试创建会话（如果方法存在）
        if hasattr(client, 'sessions'):
            print("✅ 客户端有 sessions 属性")
        
        if hasattr(client, 'config'):
            print("✅ 客户端有 config 属性")
            
        return True
        
    except Exception as e:
        print(f"❌ 时间服务器测试失败: {e}")
        return False

def test_imports():
    """测试导入"""
    print("\n📦 测试模块导入...")
    
    try:
        from mcp_use import MCPClient
        print("✅ MCPClient 导入成功")
    except ImportError as e:
        print(f"❌ MCPClient 导入失败: {e}")
        return False
    
    try:
        from mcp_use import MCPAgent
        print("✅ MCPAgent 导入成功")
    except ImportError as e:
        print(f"❌ MCPAgent 导入失败: {e}")
        return False
    
    try:
        from mcp_use.adapters.langchain_adapter import LangChainAdapter
        print("✅ LangChainAdapter 导入成功")
    except ImportError as e:
        print(f"❌ LangChainAdapter 导入失败: {e}")
        return False
    
    return True

async def main():
    """主测试函数"""
    print("🚀 开始简单测试...")
    
    # 测试导入
    import_success = test_imports()
    
    if not import_success:
        print("❌ 导入测试失败，停止测试")
        return
    
    # 测试基本功能
    basic_success = await test_basic_functionality()
    
    # 测试时间服务器
    time_success = await test_time_server_simple()
    
    # 总结
    total_tests = 3
    passed_tests = sum([import_success, basic_success, time_success])
    
    print(f"\n📊 测试结果:")
    print(f"✅ 通过: {passed_tests}/{total_tests}")
    print(f"❌ 失败: {total_tests - passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败")

if __name__ == "__main__":
    asyncio.run(main())
